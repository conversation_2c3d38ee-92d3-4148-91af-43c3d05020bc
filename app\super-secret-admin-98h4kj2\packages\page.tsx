'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  MapPin,
  Clock,
  DollarSign,
  Users,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { getSecureAdminBasePath } from '@/lib/secure-config';
import { logAdminAction } from '@/lib/admin-security';

interface TourPackage {
  id: string;
  title: string;
  slug: string;
  description: string;
  type: 'package' | 'mini-package';
  price: number;
  duration: string;
  max_participants: number;
  location: string;
  featured_image?: string;
  status: 'active' | 'inactive' | 'archived';
  highlights: string[];
  inclusions: string[];
  exclusions: string[];
  created_at: string;
  updated_at: string;
}

const PACKAGE_STATUSES = [
  { value: 'active', label: 'Active', color: 'green' },
  { value: 'inactive', label: 'Inactive', color: 'gray' },
  { value: 'archived', label: 'Archived', color: 'red' }
];

const PACKAGE_TYPES = [
  { value: 'package', label: 'Full Package' },
  { value: 'mini-package', label: 'Mini Package' }
];

export default function PackagesManagementPage() {
  const router = useRouter();
  const { user, loading, isAdmin } = useAuth();
  const [packages, setPackages] = useState<TourPackage[]>([]);
  const [isLoadingPackages, setIsLoadingPackages] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterType, setFilterType] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  const packagesPerPage = 20;

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!loading && (!user || !isAdmin())) {
      router.push(`${getSecureAdminBasePath()}/login`);
    }
  }, [user, loading, isAdmin, router]);

  // Load packages (mock data for now - replace with actual API call)
  useEffect(() => {
    if (user && isAdmin()) {
      loadPackages();
    }
  }, [user, isAdmin, currentPage, searchTerm, filterStatus, filterType]);

  const loadPackages = async () => {
    try {
      setIsLoadingPackages(true);
      
      // Mock data - replace with actual API call
      const mockPackages: TourPackage[] = [
        {
          id: '1',
          title: 'Serengeti Safari Adventure',
          slug: 'serengeti-safari-adventure',
          description: 'Experience the ultimate safari adventure in the world-famous Serengeti National Park.',
          type: 'package',
          price: 2500,
          duration: '7 days',
          max_participants: 8,
          location: 'Serengeti National Park',
          featured_image: '/images/serengeti-package.jpg',
          status: 'active',
          highlights: ['Big Five wildlife viewing', 'Great Migration', 'Professional guide'],
          inclusions: ['Accommodation', 'Meals', 'Transportation', 'Park fees'],
          exclusions: ['International flights', 'Personal expenses', 'Tips'],
          created_at: '2024-01-10T14:30:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          title: 'Kilimanjaro Day Hike',
          slug: 'kilimanjaro-day-hike',
          description: 'A challenging day hike on the slopes of Mount Kilimanjaro.',
          type: 'mini-package',
          price: 150,
          duration: '1 day',
          max_participants: 12,
          location: 'Mount Kilimanjaro',
          status: 'active',
          highlights: ['Mountain views', 'Local culture', 'Professional guide'],
          inclusions: ['Guide', 'Lunch', 'Transportation'],
          exclusions: ['Personal gear', 'Tips'],
          created_at: '2024-01-12T09:15:00Z',
          updated_at: '2024-01-12T09:15:00Z'
        }
      ];

      // Apply filters
      let filteredPackages = mockPackages;
      
      if (searchTerm) {
        filteredPackages = filteredPackages.filter(pkg =>
          pkg.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          pkg.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          pkg.location.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      if (filterStatus) {
        filteredPackages = filteredPackages.filter(pkg => pkg.status === filterStatus);
      }

      if (filterType) {
        filteredPackages = filteredPackages.filter(pkg => pkg.type === filterType);
      }

      setPackages(filteredPackages);
      setTotalPages(Math.ceil(filteredPackages.length / packagesPerPage));
      
    } catch (error) {
      console.error('Error loading packages:', error);
      setMessage({ type: 'error', text: 'Failed to load packages' });
    } finally {
      setIsLoadingPackages(false);
    }
  };

  const handleDeletePackage = async (packageId: string, packageTitle: string) => {
    if (!user) return;
    
    if (!confirm(`Are you sure you want to delete "${packageTitle}"? This action cannot be undone.`)) {
      return;
    }

    try {
      // Mock API call - replace with actual implementation
      console.log(`Deleting package ${packageId}`);
      
      // Update local state
      setPackages(prev => prev.filter(pkg => pkg.id !== packageId));
      setMessage({ type: 'success', text: 'Package deleted successfully' });
      
      await logAdminAction(user.id, {
        action: 'PACKAGE_DELETED',
        resourceType: 'packages',
        resourceId: packageId,
        success: true,
        metadata: { deletedPackageTitle: packageTitle }
      });
      
    } catch (error) {
      console.error('Error deleting package:', error);
      setMessage({ type: 'error', text: 'Failed to delete package' });
    }
  };

  const getStatusColor = (status: string) => {
    const statusConfig = PACKAGE_STATUSES.find(s => s.value === status);
    return statusConfig?.color || 'gray';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Show loading screen
  if (loading || !user || !isAdmin()) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 text-blue-600 mx-auto mb-4 animate-spin" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Package Management</h1>
            <p className="text-gray-600 mt-2">Manage tour packages and mini packages</p>
          </div>
          <button
            onClick={() => {
              // Navigate to create package page - implement as needed
              console.log('Navigate to create package');
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>New Package</span>
          </button>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="h-5 w-5" />
          ) : (
            <AlertCircle className="h-5 w-5" />
          )}
          <span>{message.text}</span>
          <button
            onClick={() => setMessage(null)}
            className="ml-auto text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Packages
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                placeholder="Search by title or location..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Status
            </label>
            <select
              value={filterStatus}
              onChange={(e) => {
                setFilterStatus(e.target.value);
                setCurrentPage(1);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              {PACKAGE_STATUSES.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>

      {/* Packages Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Packages</h2>
            <div className="text-sm text-gray-500">
              {isLoadingPackages ? 'Loading...' : `${packages.length} packages`}
            </div>
          </div>
        </div>

        {isLoadingPackages ? (
          <div className="p-8 text-center">
            <Loader2 className="h-8 w-8 text-blue-600 mx-auto mb-4 animate-spin" />
            <p className="text-gray-600">Loading packages...</p>
          </div>
        ) : packages.length === 0 ? (
          <div className="p-8 text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No packages found</h3>
            <p className="text-gray-600">
              {searchTerm || filterStatus || filterType
                ? 'Try adjusting your filters to see more results.'
                : 'Get started by creating your first package.'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Package
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {packages.map((pkg) => (
                  <tr key={pkg.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-start space-x-3">
                        {pkg.featured_image && (
                          <div className="h-12 w-12 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={pkg.featured_image}
                              alt={pkg.title}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {pkg.title}
                          </div>
                          <div className="text-sm text-gray-500 line-clamp-2">
                            {pkg.description}
                          </div>
                          <div className="flex items-center mt-1 text-xs text-gray-500">
                            <MapPin className="h-3 w-3 mr-1" />
                            {pkg.location}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        pkg.type === 'package'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-purple-100 text-purple-800'
                      }`}>
                        {PACKAGE_TYPES.find(t => t.value === pkg.type)?.label || pkg.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm font-medium text-gray-900">
                        <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                        {formatCurrency(pkg.price)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Clock className="h-4 w-4 mr-1 text-gray-400" />
                        {pkg.duration}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getStatusColor(pkg.status) === 'green'
                          ? 'bg-green-100 text-green-800'
                          : getStatusColor(pkg.status) === 'red'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {PACKAGE_STATUSES.find(s => s.value === pkg.status)?.label || pkg.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(pkg.updated_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => {
                            // Navigate to view package - implement as needed
                            console.log('View package', pkg.id);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          title="View package"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            // Navigate to edit package - implement as needed
                            console.log('Edit package', pkg.id);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          title="Edit package"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeletePackage(pkg.id, pkg.title)}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                          title="Delete package"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Type
            </label>
            <select
              value={filterType}
              onChange={(e) => {
                setFilterType(e.target.value);
                setCurrentPage(1);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Types</option>
              {PACKAGE_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('');
                setFilterStatus('');
                setFilterType('');
                setCurrentPage(1);
              }}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>
