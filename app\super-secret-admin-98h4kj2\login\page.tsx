'use client';

import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Lock, Mail, AlertCircle, CheckCircle, Loader2, Shield } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { secureAdminConfig, getSecureAdminBasePath } from '@/lib/secure-config';
import { logAdminAction, recordFailedLoginAttempt, isIPBlocked } from '@/lib/admin-security';

export default function SecureAdminLoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [csrfToken, setCsrfToken] = useState<string>('');
  const [isBlocked, setIsBlocked] = useState(false);

  // Generate CSRF token on component mount
  useEffect(() => {
    const token = generateCSRFToken();
    setCsrfToken(token);
    sessionStorage.setItem('csrf_token', token);
  }, []);

  // Check if user is already authenticated
  useEffect(() => {
    const checkUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          // Check if user has admin role
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('role, status')
            .eq('id', user.id)
            .single();

          if (profileError) {
            console.error('Profile check error:', profileError);
            setIsCheckingAuth(false);
            return;
          }

          if (userProfile && userProfile.role === 'admin' && userProfile.status === 'active') {
            router.push(`${getSecureAdminBasePath()}/dashboard`);
            return;
          }
        }
        
        setIsCheckingAuth(false);
      } catch (error) {
        console.error('Auth check failed:', error);
        setIsCheckingAuth(false);
      }
    };

    checkUser();
  }, [router]);

  // Check if IP is blocked
  useEffect(() => {
    const checkIPStatus = async () => {
      try {
        const response = await fetch('/api/admin/check-ip-status');
        const data = await response.json();
        if (data.blocked) {
          setIsBlocked(true);
          setError('Your IP address has been temporarily blocked due to multiple failed login attempts. Please try again later.');
        }
      } catch (error) {
        console.error('Error checking IP status:', error);
      }
    };

    checkIPStatus();
  }, []);

  const generateCSRFToken = (): string => {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(null);
  };

  const validateForm = (): boolean => {
    if (!formData.email.trim()) {
      setError('Email is required');
      return false;
    }

    if (!formData.password.trim()) {
      setError('Password is required');
      return false;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isBlocked) {
      setError('Your IP address is temporarily blocked. Please try again later.');
      return;
    }

    if (!validateForm()) return;

    // Verify CSRF token
    const storedToken = sessionStorage.getItem('csrf_token');
    if (!storedToken || storedToken !== csrfToken) {
      setError('Security token mismatch. Please refresh the page and try again.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Attempt login
      const { data, error: authError } = await supabase.auth.signInWithPassword({
        email: formData.email.trim(),
        password: formData.password
      });

      if (authError) {
        console.error('Authentication error:', authError);
        
        // Record failed attempt
        await recordFailedLoginAttempt(formData.email, {
          nextUrl: { pathname: window.location.pathname },
          headers: new Headers({
            'user-agent': navigator.userAgent
          }),
          method: 'POST'
        } as any);

        setError('Invalid email or password. Please check your credentials and try again.');
        setIsLoading(false);
        return;
      }

      if (data.user) {
        // Check if user has admin role
        const { data: userProfile, error: profileError } = await supabase
          .from('users')
          .select('role, status, name')
          .eq('id', data.user.id)
          .single();

        if (profileError) {
          console.error('Profile fetch error:', profileError);
          setError('User profile not found. Please contact an administrator.');
          await supabase.auth.signOut();
          setIsLoading(false);
          return;
        }

        if (!userProfile) {
          setError('User profile not found. Please contact an administrator.');
          await supabase.auth.signOut();
          setIsLoading(false);
          return;
        }

        if (userProfile.status === 'suspended') {
          setError('Your account has been suspended. Please contact an administrator.');
          await supabase.auth.signOut();
          setIsLoading(false);
          return;
        }

        if (userProfile.status !== 'active') {
          setError('Your account is not active. Please contact an administrator.');
          await supabase.auth.signOut();
          setIsLoading(false);
          return;
        }

        if (userProfile.role === 'admin') {
          // Log successful login
          await logAdminAction(data.user.id, {
            action: 'LOGIN_SUCCESS',
            ipAddress: 'client-side', // Will be updated server-side
            userAgent: navigator.userAgent,
            requestPath: window.location.pathname,
            requestMethod: 'POST',
            success: true
          });

          setSuccess(`Welcome back, ${userProfile.name}! Redirecting to admin dashboard...`);
          
          // Store user info in session storage for quick access
          sessionStorage.setItem('adminUser', JSON.stringify({
            id: data.user.id,
            name: userProfile.name,
            email: data.user.email,
            role: userProfile.role
          }));

          // Clear CSRF token
          sessionStorage.removeItem('csrf_token');

          setTimeout(() => {
            router.push(`${getSecureAdminBasePath()}/dashboard`);
          }, 1500);
        } else {
          setError('Access denied. Admin privileges required.');
          await supabase.auth.signOut();
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred. Please try again.');
      setIsLoading(false);
    }
  };

  // Show loading screen while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-blue-200">Checking authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto h-16 w-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-4">
              <Shield className="h-8 w-8 text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Secure Admin Access
            </h1>
            <p className="text-blue-200">
              Authorized personnel only
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center space-x-3">
              <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg flex items-center space-x-3">
              <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
              <p className="text-green-200 text-sm">{success}</p>
            </div>
          )}

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <input type="hidden" name="csrf_token" value={csrfToken} />
            
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-blue-200 mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-blue-400" />
                </div>
                <input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-white/20 rounded-lg bg-white/10 text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm"
                  placeholder="Enter your email"
                  disabled={isLoading || isBlocked}
                  autoComplete="email"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-blue-200 mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-blue-400" />
                </div>
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="block w-full pl-10 pr-12 py-3 border border-white/20 rounded-lg bg-white/10 text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm"
                  placeholder="Enter your password"
                  disabled={isLoading || isBlocked}
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-blue-400 hover:text-blue-300"
                  disabled={isLoading || isBlocked}
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || isBlocked}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <Loader2 className="animate-spin -ml-1 mr-3 h-5 w-5" />
                  Authenticating...
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          {/* Security Notice */}
          <div className="mt-8 text-center">
            <p className="text-xs text-blue-300">
              This is a secure area. All access attempts are logged and monitored.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
