'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { getSecureAdminBasePath } from '@/lib/secure-config';

export default function SecureUserManagementRedirect() {
  const router = useRouter();
  const { user, loading, isAdmin } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (!user || !isAdmin()) {
        router.push(`${getSecureAdminBasePath()}/login`);
      } else {
        // Redirect to the original user management page but with secure authentication
        router.push('/admin/user-management');
      }
    }
  }, [user, loading, isAdmin, router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
        <p className="text-blue-200">Redirecting to user management...</p>
      </div>
    </div>
  );
}
