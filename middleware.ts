import { NextRequest, NextResponse } from 'next/server';
import { secureAdminConfig, isSecureAdminPath, isLegacyAdminPath, getSecureAdminLoginUrl } from './lib/secure-config';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Helper function to get client IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (remoteAddr) {
    return remoteAddr;
  }
  return 'unknown';
}

// Rate limiting function
function checkRateLimit(ip: string): boolean {
  if (!secureAdminConfig.security.enableRateLimit) {
    return true;
  }

  const now = Date.now();
  const key = `rate_limit_${ip}`;
  const limit = rateLimitStore.get(key);

  if (!limit || now > limit.resetTime) {
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + secureAdminConfig.rateLimiting.windowMs
    });
    return true;
  }

  if (limit.count >= secureAdminConfig.rateLimiting.maxAttempts) {
    return false;
  }

  limit.count++;
  return true;
}

// Security headers
function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers for admin routes
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  if (secureAdminConfig.security.forceHttps) {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  return response;
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const clientIP = getClientIP(request);

  // 1. Handle legacy admin paths - hide them completely
  if (isLegacyAdminPath(pathname)) {
    console.warn(`🚨 Unauthorized access attempt to legacy admin path: ${pathname} from IP: ${clientIP}`);

    // Log the attempt for security monitoring
    if (secureAdminConfig.security.enableAuditLogging) {
      // In production, this should go to a proper logging service
      console.log(`SECURITY_ALERT: Legacy admin path access attempt - Path: ${pathname}, IP: ${clientIP}, User-Agent: ${request.headers.get('user-agent')}, Time: ${new Date().toISOString()}`);
    }

    // Return 404 to hide the existence of admin routes
    return new NextResponse(null, { status: 404 });
  }

  // 2. Handle secure admin paths
  if (isSecureAdminPath(pathname)) {
    // Check IP whitelist if configured
    if (secureAdminConfig.security.ipWhitelist.length > 0) {
      if (!secureAdminConfig.security.ipWhitelist.includes(clientIP)) {
        console.warn(`🚨 IP not whitelisted for admin access: ${clientIP}`);
        return new NextResponse(null, { status: 404 });
      }
    }

    // Apply rate limiting for admin login attempts
    if (pathname.includes('/login') && request.method === 'POST') {
      if (!checkRateLimit(clientIP)) {
        console.warn(`🚨 Rate limit exceeded for IP: ${clientIP}`);
        return NextResponse.json(
          { success: false, error: 'Too many login attempts. Please try again later.' },
          { status: 429 }
        );
      }
    }

    // Force HTTPS in production
    if (secureAdminConfig.security.forceHttps &&
        secureAdminConfig.isProduction &&
        request.headers.get('x-forwarded-proto') !== 'https') {
      const httpsUrl = new URL(request.url);
      httpsUrl.protocol = 'https:';
      return NextResponse.redirect(httpsUrl);
    }

    // Add security headers and continue
    const response = NextResponse.next();
    return addSecurityHeaders(response);
  }

  // 3. Check for large request bodies on blog API endpoints (existing functionality)
  if (request.nextUrl.pathname.startsWith('/api/admin/blog') &&
      (request.method === 'POST' || request.method === 'PUT')) {

    const contentLength = request.headers.get('content-length');

    if (contentLength) {
      const sizeInBytes = parseInt(contentLength);
      const maxSizeInBytes = 10 * 1024 * 1024; // 10MB

      if (sizeInBytes > maxSizeInBytes) {
        console.warn(`Request too large: ${sizeInBytes} bytes (max: ${maxSizeInBytes})`);
        return NextResponse.json(
          {
            success: false,
            error: 'Request too large. Blog content exceeds the maximum allowed size of 10MB. Please reduce content size, compress images, or split into multiple posts.'
          },
          { status: 413 }
        );
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Legacy admin paths to hide
    '/admin/:path*',
    '/login',

    // Secure admin paths to protect
    '/super-secret-admin-98h4kj2/:path*',

    // API endpoints to protect
    '/api/admin/:path*',

    // Blog API endpoints (existing)
    '/api/admin/blog/:path*'
  ]
};
