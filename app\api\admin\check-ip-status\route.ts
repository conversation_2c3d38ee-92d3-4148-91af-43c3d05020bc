import { NextRequest, NextResponse } from 'next/server';
import { isIPBlocked } from '@/lib/admin-security';

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (remoteAddr) {
    return remoteAddr;
  }
  return 'unknown';
}

export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request);
    const blocked = await isIPBlocked(clientIP);
    
    return NextResponse.json({
      success: true,
      blocked,
      ip: clientIP
    });
  } catch (error) {
    console.error('Error checking IP status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check IP status' },
      { status: 500 }
    );
  }
}
