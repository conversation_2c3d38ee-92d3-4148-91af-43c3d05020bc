'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Shield, 
  Users, 
  FileText, 
  Calendar, 
  Settings, 
  LogOut, 
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { logAdminAction } from '@/lib/admin-security';
import { getSecureAdminBasePath } from '@/lib/secure-config';

interface DashboardStats {
  totalUsers: number;
  activeAdmins: number;
  recentLogins: number;
  failedAttempts: number;
}

export default function SecureAdminDashboard() {
  const router = useRouter();
  const { user, loading, signOut, isAdmin } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeAdmins: 0,
    recentLogins: 0,
    failedAttempts: 0
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!loading && (!user || !isAdmin())) {
      router.push(`${getSecureAdminBasePath()}/login`);
    }
  }, [user, loading, isAdmin, router]);

  // Load dashboard statistics
  useEffect(() => {
    const loadStats = async () => {
      if (!user || !isAdmin()) return;

      try {
        // Get total users
        const { count: totalUsers } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        // Get active admins
        const { count: activeAdmins } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('role', 'admin')
          .eq('status', 'active');

        // Get recent successful logins (last 24 hours)
        const { count: recentLogins } = await supabase
          .from('sas_admin_audit_log')
          .select('*', { count: 'exact', head: true })
          .eq('action', 'LOGIN_SUCCESS')
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

        // Get failed attempts (last 24 hours)
        const { count: failedAttempts } = await supabase
          .from('sas_admin_audit_log')
          .select('*', { count: 'exact', head: true })
          .eq('action', 'LOGIN_FAILED')
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

        setStats({
          totalUsers: totalUsers || 0,
          activeAdmins: activeAdmins || 0,
          recentLogins: recentLogins || 0,
          failedAttempts: failedAttempts || 0
        });

        // Log dashboard access
        await logAdminAction(user.id, {
          action: 'DASHBOARD_ACCESS',
          requestPath: window.location.pathname,
          success: true
        });

      } catch (error) {
        console.error('Error loading dashboard stats:', error);
      } finally {
        setIsLoadingStats(false);
      }
    };

    loadStats();
  }, [user, isAdmin]);

  const handleSignOut = async () => {
    if (user) {
      await logAdminAction(user.id, {
        action: 'LOGOUT',
        requestPath: window.location.pathname,
        success: true
      });
    }
    await signOut();
  };

  const navigateToSection = (section: string) => {
    router.push(`${getSecureAdminBasePath()}/${section}`);
  };

  // Show loading screen
  if (loading || !user || !isAdmin()) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-blue-200">Loading secure admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                <Shield className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Secure Admin Dashboard</h1>
                <p className="text-blue-200">Welcome back, {user.name}</p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="flex items-center space-x-2 px-4 py-2 bg-red-500/20 text-red-200 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm font-medium">Total Users</p>
                <p className="text-3xl font-bold text-white">
                  {isLoadingStats ? '...' : stats.totalUsers}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-200 text-sm font-medium">Active Admins</p>
                <p className="text-3xl font-bold text-white">
                  {isLoadingStats ? '...' : stats.activeAdmins}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-200 text-sm font-medium">Recent Logins (24h)</p>
                <p className="text-3xl font-bold text-white">
                  {isLoadingStats ? '...' : stats.recentLogins}
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-200 text-sm font-medium">Failed Attempts (24h)</p>
                <p className="text-3xl font-bold text-white">
                  {isLoadingStats ? '...' : stats.failedAttempts}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div 
            onClick={() => navigateToSection('user-management')}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">User Management</h3>
                <p className="text-blue-200 text-sm">Manage users and permissions</p>
              </div>
            </div>
          </div>

          <div 
            onClick={() => navigateToSection('audit-logs')}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-green-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Audit Logs</h3>
                <p className="text-blue-200 text-sm">View security and activity logs</p>
              </div>
            </div>
          </div>

          <div 
            onClick={() => navigateToSection('security')}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Security Settings</h3>
                <p className="text-blue-200 text-sm">Configure security policies</p>
              </div>
            </div>
          </div>

          <div 
            onClick={() => navigateToSection('bookings')}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-yellow-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Bookings</h3>
                <p className="text-blue-200 text-sm">Manage tour bookings</p>
              </div>
            </div>
          </div>

          <div 
            onClick={() => navigateToSection('content')}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-purple-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Content Management</h3>
                <p className="text-blue-200 text-sm">Manage blog posts and pages</p>
              </div>
            </div>
          </div>

          <div 
            onClick={() => navigateToSection('settings')}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 cursor-pointer hover:bg-white/20 transition-colors"
          >
            <div className="flex items-center space-x-4">
              <div className="h-12 w-12 bg-gray-500/20 rounded-lg flex items-center justify-center">
                <Settings className="h-6 w-6 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">System Settings</h3>
                <p className="text-blue-200 text-sm">Configure system preferences</p>
              </div>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-8 bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-5 w-5 text-yellow-400 flex-shrink-0" />
            <div>
              <p className="text-yellow-200 text-sm">
                <strong>Security Notice:</strong> This is a secure administrative area. All actions are logged and monitored. 
                Ensure you follow security best practices and log out when finished.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
