/**
 * Admin Security Utilities
 * Handles audit logging, session management, and security checks
 */

import { createClient } from '@supabase/supabase-js';
import { NextRequest } from 'next/server';
import { secureAdminConfig } from './secure-config';

// Create Supabase client with service role for security operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface AuditLogEntry {
  action: string;
  resourceType?: string;
  resourceId?: string;
  ipAddress?: string;
  userAgent?: string;
  requestPath?: string;
  requestMethod?: string;
  success?: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export interface AdminSession {
  id: string;
  userId: string;
  sessionToken: string;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: string;
  lastActivity: string;
  isActive: boolean;
}

/**
 * Log admin action to audit table
 */
export async function logAdminAction(
  userId: string | null,
  entry: AuditLogEntry
): Promise<string | null> {
  if (!secureAdminConfig.security.enableAuditLogging) {
    return null;
  }

  try {
    const { data, error } = await supabaseAdmin.rpc('log_admin_action', {
      p_action: entry.action,
      p_resource_type: entry.resourceType || null,
      p_resource_id: entry.resourceId || null,
      p_ip_address: entry.ipAddress || null,
      p_user_agent: entry.userAgent || null,
      p_request_path: entry.requestPath || null,
      p_request_method: entry.requestMethod || null,
      p_success: entry.success ?? true,
      p_error_message: entry.errorMessage || null,
      p_metadata: entry.metadata || {}
    });

    if (error) {
      console.error('Failed to log admin action:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error logging admin action:', error);
    return null;
  }
}

/**
 * Create admin session
 */
export async function createAdminSession(
  userId: string,
  sessionToken: string,
  request: NextRequest
): Promise<AdminSession | null> {
  try {
    const expiresAt = new Date(Date.now() + secureAdminConfig.sessionTimeout);
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    const { data, error } = await supabaseAdmin
      .from('sas_admin_sessions')
      .insert({
        user_id: userId,
        session_token: sessionToken,
        ip_address: ipAddress,
        user_agent: userAgent,
        expires_at: expiresAt.toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Failed to create admin session:', error);
      return null;
    }

    // Log session creation
    await logAdminAction(userId, {
      action: 'SESSION_CREATED',
      ipAddress,
      userAgent,
      requestPath: request.nextUrl.pathname,
      requestMethod: request.method
    });

    return {
      id: data.id,
      userId: data.user_id,
      sessionToken: data.session_token,
      ipAddress: data.ip_address,
      userAgent: data.user_agent,
      expiresAt: data.expires_at,
      lastActivity: data.last_activity,
      isActive: data.is_active
    };
  } catch (error) {
    console.error('Error creating admin session:', error);
    return null;
  }
}

/**
 * Validate admin session
 */
export async function validateAdminSession(sessionToken: string): Promise<AdminSession | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('sas_admin_sessions')
      .select('*')
      .eq('session_token', sessionToken)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) {
      return null;
    }

    // Update last activity
    await supabaseAdmin
      .from('sas_admin_sessions')
      .update({ last_activity: new Date().toISOString() })
      .eq('id', data.id);

    return {
      id: data.id,
      userId: data.user_id,
      sessionToken: data.session_token,
      ipAddress: data.ip_address,
      userAgent: data.user_agent,
      expiresAt: data.expires_at,
      lastActivity: data.last_activity,
      isActive: data.is_active
    };
  } catch (error) {
    console.error('Error validating admin session:', error);
    return null;
  }
}

/**
 * Invalidate admin session
 */
export async function invalidateAdminSession(sessionToken: string): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('sas_admin_sessions')
      .update({ is_active: false })
      .eq('session_token', sessionToken);

    if (error) {
      console.error('Failed to invalidate admin session:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error invalidating admin session:', error);
    return false;
  }
}

/**
 * Record failed login attempt
 */
export async function recordFailedLoginAttempt(
  email: string | null,
  request: NextRequest
): Promise<void> {
  try {
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    // Check if there's an existing record for this IP
    const { data: existing } = await supabaseAdmin
      .from('sas_failed_login_attempts')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('email', email || '')
      .single();

    if (existing) {
      // Update existing record
      await supabaseAdmin
        .from('sas_failed_login_attempts')
        .update({
          attempt_count: existing.attempt_count + 1,
          last_attempt: new Date().toISOString(),
          blocked_until: existing.attempt_count >= 4 ? 
            new Date(Date.now() + 30 * 60 * 1000).toISOString() : // Block for 30 minutes after 5 attempts
            null
        })
        .eq('id', existing.id);
    } else {
      // Create new record
      await supabaseAdmin
        .from('sas_failed_login_attempts')
        .insert({
          email: email || null,
          ip_address: ipAddress,
          user_agent: userAgent,
          attempt_count: 1
        });
    }

    // Log the failed attempt
    await logAdminAction(null, {
      action: 'LOGIN_FAILED',
      ipAddress,
      userAgent,
      requestPath: request.nextUrl.pathname,
      requestMethod: request.method,
      success: false,
      metadata: { email: email || 'unknown' }
    });
  } catch (error) {
    console.error('Error recording failed login attempt:', error);
  }
}

/**
 * Check if IP is blocked due to failed attempts
 */
export async function isIPBlocked(ipAddress: string): Promise<boolean> {
  try {
    const { data } = await supabaseAdmin
      .from('sas_failed_login_attempts')
      .select('blocked_until')
      .eq('ip_address', ipAddress)
      .gt('blocked_until', new Date().toISOString())
      .single();

    return !!data;
  } catch (error) {
    return false;
  }
}

/**
 * Verify user is active admin
 */
export async function verifyActiveAdmin(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin.rpc('is_active_admin', {
      user_id: userId
    });

    if (error) {
      console.error('Error verifying admin status:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Error verifying admin status:', error);
    return false;
  }
}

/**
 * Get client IP from request
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const remoteAddr = request.headers.get('x-vercel-forwarded-for');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (remoteAddr) {
    return remoteAddr;
  }
  return 'unknown';
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin.rpc('cleanup_expired_admin_sessions');

    if (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }

    return data || 0;
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error);
    return 0;
  }
}
