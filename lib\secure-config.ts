/**
 * Secure Admin Configuration
 * Centralized configuration for secure admin authentication system
 */

export const secureAdminConfig = {
  // Obfuscated admin path - never expose this in client-side code
  adminPath: process.env.SECURE_ADMIN_PATH || 'super-secret-admin-98h4kj2',
  
  // Session configuration
  sessionSecret: process.env.ADMIN_SESSION_SECRET || 'fallback-secret-change-in-production',
  sessionTimeout: parseInt(process.env.ADMIN_SESSION_TIMEOUT_MS || '3600000'), // 1 hour default
  
  // Rate limiting configuration
  rateLimiting: {
    maxAttempts: parseInt(process.env.ADMIN_RATE_LIMIT_MAX_ATTEMPTS || '5'),
    windowMs: parseInt(process.env.ADMIN_RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes default
  },
  
  // CSRF protection
  csrfSecret: process.env.ADMIN_CSRF_SECRET || 'fallback-csrf-secret-change-in-production',
  
  // Security features
  security: {
    enableAuditLogging: process.env.ENABLE_ADMIN_AUDIT_LOGGING === 'true',
    ipWhitelist: process.env.ADMIN_IP_WHITELIST?.split(',').filter(Boolean) || [],
    forceHttps: process.env.FORCE_HTTPS_ADMIN === 'true',
    enableCSRF: true,
    enableRateLimit: true,
  },
  
  // Environment checks
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
};

/**
 * Get the full secure admin login URL
 */
export function getSecureAdminLoginUrl(): string {
  return `/${secureAdminConfig.adminPath}/login`;
}

/**
 * Get the secure admin base path
 */
export function getSecureAdminBasePath(): string {
  return `/${secureAdminConfig.adminPath}`;
}

/**
 * Check if a path is a secure admin path
 */
export function isSecureAdminPath(pathname: string): boolean {
  return pathname.startsWith(`/${secureAdminConfig.adminPath}`);
}

/**
 * Check if a path is a legacy admin path that should be hidden
 */
export function isLegacyAdminPath(pathname: string): boolean {
  const legacyPaths = ['/admin', '/admin/login', '/login'];
  return legacyPaths.some(path => pathname === path || pathname.startsWith(path + '/'));
}

/**
 * Validate configuration on startup
 */
export function validateSecureConfig(): void {
  const errors: string[] = [];
  
  if (!process.env.SECURE_ADMIN_PATH) {
    errors.push('SECURE_ADMIN_PATH environment variable is required');
  }
  
  if (!process.env.ADMIN_SESSION_SECRET || process.env.ADMIN_SESSION_SECRET.length < 32) {
    errors.push('ADMIN_SESSION_SECRET must be at least 32 characters long');
  }
  
  if (!process.env.ADMIN_CSRF_SECRET || process.env.ADMIN_CSRF_SECRET.length < 32) {
    errors.push('ADMIN_CSRF_SECRET must be at least 32 characters long');
  }
  
  if (secureAdminConfig.isProduction && secureAdminConfig.adminPath === 'super-secret-admin-98h4kj2') {
    errors.push('Default admin path should be changed in production');
  }
  
  if (errors.length > 0) {
    console.error('❌ Secure Admin Configuration Errors:');
    errors.forEach(error => console.error(`   - ${error}`));
    
    if (secureAdminConfig.isProduction) {
      throw new Error('Invalid secure admin configuration in production');
    }
  }
}

// Validate configuration on module load
if (typeof window === 'undefined') { // Server-side only
  validateSecureConfig();
}
