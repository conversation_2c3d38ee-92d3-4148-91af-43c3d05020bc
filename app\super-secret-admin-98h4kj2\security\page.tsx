'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Shield, 
  Lock, 
  AlertTriangle, 
  CheckCircle, 
  Settings,
  Eye,
  EyeOff,
  Save,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { getSecureAdminBasePath, secureAdminConfig } from '@/lib/secure-config';

export default function SecuritySettingsPage() {
  const router = useRouter();
  const { user, loading, isAdmin } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  // Security settings state
  const [settings, setSettings] = useState({
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    sessionTimeout: 60,
    requireHttps: true,
    enableRateLimit: true,
    enableIpWhitelist: false,
    whitelistedIps: '',
    enableAuditLogging: true,
    logRetentionDays: 90
  });

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!loading && (!user || !isAdmin())) {
      router.push(`${getSecureAdminBasePath()}/login`);
    }
  }, [user, loading, isAdmin, router]);

  // Load current settings
  useEffect(() => {
    const loadSettings = () => {
      // Load from secure config
      setSettings({
        maxLoginAttempts: secureAdminConfig.rateLimiting.maxAttempts,
        lockoutDuration: secureAdminConfig.rateLimiting.windowMs / (1000 * 60), // Convert to minutes
        sessionTimeout: secureAdminConfig.session.maxAge / (1000 * 60), // Convert to minutes
        requireHttps: secureAdminConfig.security.requireHttps,
        enableRateLimit: secureAdminConfig.rateLimiting.enabled,
        enableIpWhitelist: secureAdminConfig.security.ipWhitelist.length > 0,
        whitelistedIps: secureAdminConfig.security.ipWhitelist.join('\n'),
        enableAuditLogging: true, // Always enabled for security
        logRetentionDays: 90 // Default retention
      });
    };

    if (user && isAdmin()) {
      loadSettings();
    }
  }, [user, isAdmin]);

  const handleInputChange = (field: string, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
    if (message) setMessage(null);
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    setMessage(null);

    try {
      // In a real implementation, you would save these to a database
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      setMessage({
        type: 'success',
        text: 'Security settings updated successfully. Changes will take effect after server restart.'
      });

    } catch (error) {
      console.error('Error saving settings:', error);
      setMessage({
        type: 'error',
        text: 'Failed to save security settings. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      maxLoginAttempts: 5,
      lockoutDuration: 15,
      sessionTimeout: 60,
      requireHttps: true,
      enableRateLimit: true,
      enableIpWhitelist: false,
      whitelistedIps: '',
      enableAuditLogging: true,
      logRetentionDays: 90
    });
    setMessage(null);
  };

  // Show loading screen
  if (loading || !user || !isAdmin()) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-blue-200">Loading security settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push(`${getSecureAdminBasePath()}/dashboard`)}
                className="h-10 w-10 bg-blue-500/20 rounded-full flex items-center justify-center hover:bg-blue-500/30 transition-colors"
              >
                <Shield className="h-6 w-6 text-blue-400" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-white">Security Settings</h1>
                <p className="text-blue-200">Configure administrative security policies</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={resetToDefaults}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-500/20 text-gray-200 rounded-lg hover:bg-gray-500/30 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Reset</span>
              </button>
              <button
                onClick={handleSaveSettings}
                disabled={isLoading}
                className="flex items-center space-x-2 px-4 py-2 bg-green-500/20 text-green-200 rounded-lg hover:bg-green-500/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                <span>{isLoading ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg border flex items-center space-x-3 ${
            message.type === 'success' 
              ? 'bg-green-500/20 border-green-500/30 text-green-200' 
              : 'bg-red-500/20 border-red-500/30 text-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 flex-shrink-0" />
            ) : (
              <AlertTriangle className="h-5 w-5 flex-shrink-0" />
            )}
            <p className="text-sm">{message.text}</p>
          </div>
        )}

        {/* Authentication Settings */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-6">
          <div className="flex items-center space-x-3 mb-6">
            <Lock className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Authentication Settings</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">
                Max Login Attempts
              </label>
              <input
                type="number"
                min="1"
                max="20"
                value={settings.maxLoginAttempts}
                onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-blue-300 mt-1">Number of failed attempts before lockout</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">
                Lockout Duration (minutes)
              </label>
              <input
                type="number"
                min="1"
                max="1440"
                value={settings.lockoutDuration}
                onChange={(e) => handleInputChange('lockoutDuration', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-blue-300 mt-1">How long to block IP after failed attempts</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">
                Session Timeout (minutes)
              </label>
              <input
                type="number"
                min="5"
                max="480"
                value={settings.sessionTimeout}
                onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-blue-300 mt-1">Automatic logout after inactivity</p>
            </div>
          </div>
        </div>

        {/* Security Policies */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-6">
          <div className="flex items-center space-x-3 mb-6">
            <Shield className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Security Policies</h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">Require HTTPS</h3>
                <p className="text-sm text-blue-300">Force secure connections for all admin access</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.requireHttps}
                  onChange={(e) => handleInputChange('requireHttps', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">Enable Rate Limiting</h3>
                <p className="text-sm text-blue-300">Limit login attempts to prevent brute force attacks</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.enableRateLimit}
                  onChange={(e) => handleInputChange('enableRateLimit', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">Enable Audit Logging</h3>
                <p className="text-sm text-blue-300">Log all administrative actions for security monitoring</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.enableAuditLogging}
                  onChange={(e) => handleInputChange('enableAuditLogging', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* IP Whitelist */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <Settings className="h-6 w-6 text-blue-400" />
              <h2 className="text-xl font-semibold text-white">IP Whitelist</h2>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableIpWhitelist}
                onChange={(e) => handleInputChange('enableIpWhitelist', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {settings.enableIpWhitelist && (
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">
                Whitelisted IP Addresses (one per line)
              </label>
              <textarea
                value={settings.whitelistedIps}
                onChange={(e) => handleInputChange('whitelistedIps', e.target.value)}
                rows={6}
                placeholder="*************&#10;*********&#10;************"
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-blue-300 mt-1">
                Only these IP addresses will be allowed to access admin areas
              </p>
            </div>
          )}
        </div>

        {/* Log Retention */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
          <div className="flex items-center space-x-3 mb-6">
            <Eye className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Log Retention</h2>
          </div>

          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">
              Retention Period (days)
            </label>
            <input
              type="number"
              min="7"
              max="365"
              value={settings.logRetentionDays}
              onChange={(e) => handleInputChange('logRetentionDays', parseInt(e.target.value))}
              className="w-full max-w-xs px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-blue-300 mt-1">
              How long to keep audit logs before automatic deletion
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
